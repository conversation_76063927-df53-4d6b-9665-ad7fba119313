@extends('layouts.default')
@push('title',"Thanh toán ". $course_details->title)
@push('meta')@endpush
@push('css')
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/styles_shopee.css') }}"/>
    <link
        rel="stylesheet"
        href="{{ asset('assets/frontend/default/libs/aos/aos-*******-beta.6.css') }}"
    />
    <link rel="stylesheet" href="{{ asset('assets/css/checkout.css') }}"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css"/>
    <script src="{{ asset('assets/frontend/mst-academy/assets/libs/aos/aos-*******-beta.6.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    @if (get_frontend_settings('recaptcha_status'))
        <script src="https://www.google.com/recaptcha/api.js?render={{ get_frontend_settings('recaptcha_sitekey') }}"></script>
    @endif
<style>
    .modal-regis .user-info-container a {
        cursor: pointer;
    }

    /* Authenticated user styles */
    .user-info-container .text-muted {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .user-info-container .text-muted i {
        margin-right: 8px;
    }

    /* Loading button styles */
    #btn-checkout-regis:disabled {
        background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
        opacity: 0.8;
        cursor: not-allowed;
    }

    #btn-checkout-regis .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Professional loading effect */
    #btn-checkout-regis:disabled {
        position: relative;
        overflow: hidden;
    }

    #btn-checkout-regis:disabled::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: loading-shimmer 1.5s infinite;
    }

    @keyframes loading-shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* Payment methods styling */
    .payment-methods-container .method-option {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        background: #fff;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .payment-methods-container .method-option:hover {
        border-color: #ff6b35;
        box-shadow: 0 4px 12px rgba(255, 107, 53, 0.1);
    }

    .payment-methods-container .method-title {
        font-size: 16px;
        color: #333;
        margin-bottom: 20px;
        line-height: 1.4;
    }

    .payment-methods-container .qr-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .payment-methods-container .body-method-option {
        flex: 1;
    }

    .payment-methods-container .bank-info-row {
        margin-bottom: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .payment-methods-container .bank-info-row:last-child {
        border-bottom: none;
    }

    .payment-methods-container .bank-info-label {
        font-weight: 600;
        color: #666;
        min-width: 100px;
    }

    .payment-methods-container .bank-info-value {
        font-weight: 500;
        color: #333;
        text-align: right;
        flex: 1;
    }

    .payment-methods-container .copy-value {
        margin-left: 8px;
        cursor: pointer;
        color: #ff6b35 !important;
        transition: color 0.2s ease;
    }

    .payment-methods-container .copy-value:hover {
        color: #e55a2b !important;
    }

    .payment-note {
        background: #fff9e6;
        border: 1px solid #ffd700;
        border-radius: 8px;
        padding: 20px;
    }

    .payment-note p {
        margin-bottom: 10px;
        color: #b8860b;
        font-weight: 600;
    }

    .payment-note ul {
        margin-bottom: 0;
        color: #8b7355;
    }

    .payment-note li {
        margin-bottom: 8px;
        line-height: 1.5;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .payment-methods-container .method-option {
            margin-bottom: 20px;
        }

        .payment-methods-container .method-title {
            text-align: center;
            font-size: 14px;
        }

        .payment-methods-container .bank-info-row {
            flex-direction: column;
            align-items: flex-start;
            text-align: left;
        }

        .payment-methods-container .bank-info-value {
            text-align: left;
            margin-top: 4px;
        }
    }

    /* Payment Tab Styles */
    .payment-methods-container {
        padding: 20px;
    }

    .method-option {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        height: 100%;
    }

    .method-title {
        font-size: 16px;
        margin-bottom: 15px;
        color: #333;
    }

    .qr-container {
        text-align: center;
        position: relative;
    }

    .border-bank {
        max-width: 200px;
        margin-bottom: 10px;
    }

    #qrcode {
        position: relative;
        display: inline-block;
        max-width: 200px;
        margin: 0 auto;
    }

    #qrcode img {
        width: 100%;
        height: auto;
        border-radius: 8px;
    }

    .scanner {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 2px solid #007bff;
        border-radius: 8px;
        animation: scan 2s infinite;
    }

    @keyframes scan {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.3; }
    }

    .bank-info {
        background: white;
        border-radius: 8px;
        padding: 15px;
    }

    .bank-info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .bank-info-row:last-child {
        border-bottom: none;
    }

    .bank-info-label {
        font-weight: 600;
        color: #666;
        min-width: 120px;
    }

    .bank-info-value {
        font-weight: 500;
        color: #333;
        text-align: right;
        flex: 1;
    }

    .copy-value {
        cursor: pointer;
        margin-left: 8px;
        color: #007bff;
        transition: color 0.2s;
    }

    .copy-value:hover {
        color: #0056b3;
    }

    .payment-note {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }

    .payment-note p {
        margin-bottom: 10px;
        font-weight: 600;
        color: #856404;
    }

    .payment-note ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .payment-note li {
        margin-bottom: 5px;
        color: #856404;
    }
</style>
@endpush



@section('content')

    <div class="tax-landing main">
        <div class="tax-banner" style="background-image: url('{{ asset('assets/img/bg-checkout.png') }}');">
            <div class="container">
                <!-- Main Title -->
                <h1 class="main-title">
                    ĐỪNG ĐỂ RỦI RO VỀ THUẾ!!!
                </h1>

                <!-- Subtitle -->
                <div class="subtitle">
                    <strong style="color: #FBC30B;">1 Bước cuối</strong> cùng áp dụng hiệu quả các quy định về thuế, tối
                    ưu hóa<br>
                    nghĩa vụ thuế, giảm rủi ro pháp lý trong <strong>14 ngày!!!</strong><br>
                    <strong>Bắt đầu ngay hôm nay!</strong>
                </div>


                <div class="d-flex justify-content-center arrow-container">
                    <img src="{{ asset('assets/img/arrow-bot-animation.png') }}" alt="Arrow pointing down"

                         width="42"
                         height="50"
                         class="w-auto h-9 md:h-10 lg:h-[50px] animate-bounce"
                         data-aos-lazy="fade-down"
                         data-aos-delay="300"
                         data-aos-duration="1000"
                         data-aos-repeat="true">
                </div>

                <!-- Promo Card -->
                <div class="promo-card">
                    <div class="row align-items-center">
                        <img class="rocket-icon" src="{{ asset('assets/img/rocket-icon.png') }}" alt="Rocket Icon"
                             width="80" height="80">
                        <div class="col">
                            <h3>200 doanh nghiệp dùng thuế không sai phạm</h3>
                            <p>
                                Là con số mà TopID đã hỗ trợ trực tiếp và bây giờ bạn hoàn toàn làm được theo quy
                                trình mà TopID đã soạn sẵn!!!
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Slots Section -->
                <div class="slots-section">
                    <h2 class="slots-title">
                        <span style="color: #FBC30B;">7 SLOT</span> CUỐI CÙNG
                    </h2>
                    <p class="slots-subtitle">
                        Nhận ngay ưu đãi hấp dẫn khi mua khóa học tối ưu<br>
                        hóa nghĩa vụ thuế, giảm rủi ro pháp lý!
                    </p>

                    <!-- Sparkle Effects -->
                    <div class="absolute inset-0 z-0">
                        <div class="sparkle"
                             style="width: 12px; height: 12px; top: -15%; left: 10%; animation-delay: 0s;"></div>
                        <div class="sparkle"
                             style="width: 8px; height: 8px; top: 10%; left: 95%; animation-delay: 0.2s;"></div>
                        <div class="sparkle"
                             style="width: 10px; height: 10px; top: 80%; left: 105%; animation-delay: 0.4s;"></div>
                        <div class="sparkle"
                             style="width: 14px; height: 14px; top: 105%; left: 60%; animation-delay: 0.6s;"></div>
                        <div class="sparkle"
                             style="width: 9px; height: 9px; top: 60%; left: -5%; animation-delay: 0.8s;"></div>
                        <div class="sparkle"
                             style="width: 11px; height: 11px; top: 30%; left: -10%; animation-delay: 1s;"></div>
                        <div class="sparkle"
                             style="width: 7px; height: 7px; top: 90%; left: 25%; animation-delay: 1.2s;"></div>
                    </div>
                    <div class="progress-container flex items-center justify-center max-w-2xl mx-auto">

                        <img
                            src="{{ asset('assets/img/gift.png') }}"
                            alt="Biểu tượng hộp quà"
                            class="gift-icon"
                        >

                        <div class="progress progress-bar-custom" role="progressbar"
                             aria-label="Default striped example" aria-valuenow="10" aria-valuemin="0"
                             aria-valuemax="100">
                            <div class="progress-bar progress-bar-striped" style="width: 30%"></div>
                        </div>

                    </div>


                    <!-- Dots Pattern -->
                    <div class="story__dots animation-dots">
                        <img src="{{ asset('assets/img/section-below.png') }}" alt="">
                    </div>
                </div>
            </div>
        </div>

        <div class="section-buy" style="background-image: url('{{ asset('assets/img/bg-buynow.png') }}');">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="buy-content">
                            <img src="{{ asset('assets/img/special-tag.png') }}" alt="" class="special-tag">

                            <h5 class="buy-title">ƯU ĐÃI KHI MUA KHÓA HỌC</h5>

                            <div class="benefits-list">
                                <div class="benefit-item">
                                    <div class="benefit-icon">✓</div>
                                    <div class="d-flex flex-column">
                                        <span class="b-box">Được tham gia free</span>
                                        <span class="highlight-text">Tất cả các buổi workshop và offline định kỳ</span>
                                    </div>
                                </div>
                                <div class="benefit-item">
                                    <div class="benefit-icon">✓</div>
                                    <div class="d-flex flex-column">
                                        <span class="b-box">Free Mentor Marketing</span>
                                        <span class="highlight-text">Marketing Manager ở TopID</span>
                                    </div>
                                </div>
                                <div class="benefit-item">
                                    <div class="benefit-icon">✓</div>
                                    <div class="d-flex flex-column">
                                        <span class="b-box">Hỗ trợ bạn xây dựng khóa học</span>
                                        <span class="highlight-text">Xây dựng khóa học bài bản cùng đội ngũ support liên tục, đẩy khóa học ra mắt nhanh (case nhanh nhất 5 ngày)</span>
                                    </div>
                                </div>
                                <div class="benefit-item">
                                    <div class="benefit-icon">✓</div>
                                    <div class="d-flex flex-column">
                                        <span class="b-box">Trợ giá 15%</span>
                                        <span class="highlight-text">Các dịch vụ hình ảnh và video</span>
                                    </div>
                                </div>
                                <div class="benefit-item">
                                    <div class="benefit-icon">✓</div>
                                    <div class="d-flex flex-column">
                                        <span class="b-box">Trợ giá 10%</span>
                                        <span class="highlight-text">Các dịch vụ video ads và xây kênh</span>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="course-preview">
                            <div class="course-image-container">
                                <img src="https://elearning.topid.vn/uploads/course-thumbnail/-1744433510.png"
                                     class="course-image">


                                <div class="play-overlay" onclick="playVideo()">
                                    <div class="play-button">
                                        <i class="fas fa-play"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 modal-regis">
                        <div class="regis-content">

                            <div class="hot-deal">
                                <div class="hot-deal__original-price">4.000.000đ</div>
                                <div class="hot-deal__promo">
                                    <span class="hot-deal__promo-label">Chỉ còn:</span>
                                    <strong class="hot-deal__promo-price">2.800.000đ</strong>
                                    <img class="hot-deal__promo-icon" src="{{ asset('assets/img/hot-deal.svg') }}"
                                         alt="Hot Deal Icon">
                                </div>
                            </div>

                            <div class="price-info">
                                <span>Gói đã chọn:</span>
                                <span id="selected-plan-name">Trọn đời</span>
                            </div>
                            <div class="price-info">
                                <span>Giá gốc:</span>
                                <span class="text-decoration-line-through"
                                      id="original-price-display">2.990.000&nbsp;₫</span>
                            </div>
                            <div class="price-info">
                                <span>Giá ưu đãi hôm nay:</span>
                                <strong id="current-price-display">2.990.000&nbsp;₫</strong>
                            </div>
                            <div class="input-group mt-3" id="coupon_form">
                                <input type="text" class="form-control" id="couponInput" placeholder="Mã ưu đãi">
                                <button class="btn code-btn text-white" type="button" id="applyCoupon">
                                    Áp dụng
                                </button>
                            </div>
                            <div class="total-price">
                                <span class="fw-bold">Tổng thanh toán:</span>
                                <span class="price" id="total_amount">2.990.000&nbsp;₫</span>
                            </div>

                            <form action="{{ route('checkout.course') }}" method="POST" id="form-checkout-regis">
                                @csrf
                                <input type="hidden" id="coupon_code_hide" name="coupon_code" value="">
                                <input type="hidden" id="course_id_hide" name="course_id" value="{{ $course_details->id }}">
                                <input type="hidden" name="pricing_plan_id" id="pricing_plan_id" value="">

                                <div class="user-info-container">
                                    @auth
                                        <h6 class="fw-bold mb-3">Xin chào, {{ auth()->user()->name }}!</h6>
                                        <p class="mb-4 text-muted">
                                            <i class="bi bi-check-circle text-success"></i>
                                            Bạn đã đăng nhập. Click "Mua ngay" để tiếp tục.
                                        </p>
                                    @else
                                        <h6 class="fw-bold mb-3">Thông tin của bạn:</h6>
                                        <p class="mb-4 change-login-register">
                                            Bạn đã có tài khoản?
                                            <a class="login-link" style="cursor: pointer; color: #007bff; text-decoration: underline;">Ấn vào đây
                                                để đăng nhập</a>
                                        </p>
                                    @endauth

                                @guest
                                    <div class="mb-4 form-wrap">
                                        <label for="email" class="form-label">Email *:</label>
                                        <input type="email" value="{{ old('email') }}" class="form-control" name="email"
                                               id="payment_register_email" required/>
                                        @error('email')
                                        <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div class="mb-4 form-wrap">
                                        <label for="password" class="form-label">Mật khẩu *:</label>
                                        <div class="password-field">
                                            <input type="password" class="form-control" name="password"
                                                   id="payment_register_password" required/>
                                            <button class="password-toggle" type="button">
                                                <i class="bi bi-eye-slash"></i>
                                            </button>
                                        </div>
                                        @error('password')
                                        <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <div class="mb-4 form-wrap is-show">
                                        <label for="phone" class="form-label">Số điện thoại *:</label>
                                        <div class="input-group phone-group">
                                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                                    data-bs-toggle="dropdown">
                                                <img
                                                    src="{{ asset('assets/frontend/course-shopee/assets/images/vn.png') }}"
                                                    alt="Vietnam Flag" width="20" height="15"/>
                                                +84
                                            </button>
                                            <input type="tel" value="{{ old('phone') }}" class="form-control" name="phone"
                                                   id="payment_register_phone"/>

                                        </div>
                                        @error('phone')
                                        <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                @endguest




                                <div>
                                    <div class="grecaptcha-badge" data-style="bottomright"
                                         style="width: 256px; height: 60px; display: block; transition: right 0.3s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;">
                                        <div class="grecaptcha-logo">
                                            <iframe title="reCAPTCHA" width="256" height="60" role="presentation"
                                                    name="a-jp1jqozf32rh" frameborder="0" scrolling="no"
                                                    sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation"
                                                    src="https://www.google.com/recaptcha/api2/anchor?ar=1&amp;k=6LeB1BUrAAAAADVvLh3_4bp6OoqRDBkTT1gh0G-q&amp;co=aHR0cHM6Ly9lbGVhcm5pbmcudG9waWQudm46NDQz&amp;hl=en&amp;v=h7qt2xUGz2zqKEhSc8DD8baZ&amp;size=invisible&amp;sa=submit&amp;cb=v2exle5xpjl7"></iframe>
                                        </div>
                                        <div class="grecaptcha-error"></div>
                                        <textarea id="g-recaptcha-response" name="g-recaptcha-response"
                                                  class="g-recaptcha-response"
                                                  style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea>
                                    </div>
                                </div>
                                    @if (get_frontend_settings('recaptcha_status'))
                                        <button class="btn g-recaptcha g-recaptcha-checkout" id="btn-checkout-regis"
                                                data-sitekey="{{ get_frontend_settings('recaptcha_sitekey') }}"
                                                data-callback="onHandlecheckoutPayment" data-action="submit" type="button">
                                            Mua ngay
                                        </button>
                                    @else
                                        <button class="btn" id="btn-checkout-regis" type="button">
                                            Mua ngay
                                        </button>
                                    @endif

                                </div>
                            </form>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <div class="section-solution solution"
             style="background-image: url('{{ asset('assets/img/bg-solution.png') }}');">
            <div class="text-center course-detail-title">Chi tiết khóa học</div>
            <div class="course-detail-desc">Khóa học gồm
                <div class="module-count">{{ $total_lesson }} Bài Học</div>
                với nội dung cực kỳ thực tế.
            </div>
            <div class="container">
                <div class="shopee-course-interface">
                    <div class="d-flex course-container">

                        @if ($sections->count() > 0)
                            <div class="course-list w-full" style="width: 100% !important;">
                                <div
                                    class="accordion modules-list accordion-flush"
                                    id="course-modules-accordion"
                                >
                                    @foreach ($sections as $key => $section)
                                        @php
                                            $lessons = App\Models\Lesson::where('section_id', $section->id)
                                                ->orderBy('sort')
                                                ->get();

                                            // Calculate total duration and video count for this section
                                            $total_duration = 0;
                                            $video_count = 0;
                                            foreach ($lessons as $lesson) {
                                                $duration = lesson_durations($lesson->id);
                                                if ($duration != '00:00:00') {
                                                    $time_parts = explode(':', $duration);
                                                    if (count($time_parts) == 3) {
                                                        $total_duration += ($time_parts[0] * 3600) + ($time_parts[1] * 60) + $time_parts[2];
                                                    }
                                                }

                                                if (in_array($lesson->lesson_type, ['video-url', 'system-video', 'vimeo-url', 'google_drive','iframe'])) {
                                                    $video_count++;
                                                }
                                            }

                                            $hours = floor($total_duration / 3600);
                                            $minutes = floor(($total_duration % 3600) / 60);
                                            $seconds = $total_duration % 60;
                                            $formatted_duration = sprintf("%02d:%02d", $hours, $minutes);
                                        @endphp
                                        <div class="module-item accordion-item">
                                            <div
                                                class="module-header accordion-header d-flex justify-content-between align-items-center"
                                            >
                                                <div
                                                    data-bs-toggle="collapse"
                                                    data-bs-target="#course-module-content-{{ $section->id }}"
                                                    aria-expanded="{{ $key == 0 ? 'true' : 'false' }}"
                                                    aria-controls="course-module-content-{{ $section->id }}"
                                                    class="{{ $key == 0 ? 'a' : 'collapsed' }}"
                                                >
                                                    <div class="module-title">
                                                        <h3 class="text-h6 mb-0">
                                                            {{ ucfirst($section->title) }}
                                                        </h3>
                                                        <div class="module-details">
                                                <span class="video-count text-text14">
                                                    <img
                                                        src="{{ asset('assets/frontend/default/images_shopee/video-icon.svg')}}"
                                                        alt="Video"
                                                    />
                                                    <span>
                                                        Số lượng video:
                                                        <strong>{{ $video_count }}</strong>
                                                    </span>
                                                </span>
                                                            <span class="duration text-text14">
                                                    <img
                                                        src="{{ asset('assets/frontend/default/images_shopee/clock-icon.svg')}}"
                                                        alt="Clock"
                                                    />
                                                    <span>
                                                        Thời lượng:
                                                        <strong>{{ $formatted_duration }}</strong>
                                                    </span>
                                                </span>
                                                        </div>
                                                    </div>
                                                    <div class="toggle-btn">
                                                        <img
                                                            class="toggle-btn-icon toggle-btn-icon-collapse"
                                                            src="{{ asset('assets/frontend/default/images_shopee/minus.svg')}}"
                                                            alt="Collapse"
                                                        />
                                                        <img
                                                            class="toggle-btn-icon toggle-btn-icon-expand"
                                                            src="{{ asset('assets/frontend/default/images_shopee/plus.svg')}}"
                                                            alt="Expand"
                                                        />
                                                    </div>
                                                </div>
                                            </div>

                                            <div
                                                class="module-content accordion-collapse collapse @if($key == 0) show @endif"
                                                id="course-module-content-{{ $section->id }}"
                                                data-bs-parent="#course-modules-accordion"
                                            >
                                                <div class="accordion-body">
                                                    {{--                                                        <p class="module-content-description">--}}
                                                    {{--                                                            Cách mở gian hàng, cách xác định sản phẩm--}}
                                                    {{--                                                            tiềm năng để bắt đầu bán hàng trên Shopee.--}}
                                                    {{--                                                        </p>--}}

                                                    @foreach ($lessons as $lesson)
                                                        @php
                                                            $duration = lesson_durations($lesson->id);
                                                        @endphp
                                                        <div class="lesson-item">
                                                            <a href="{{ route('course.player', ['slug' => $course_details->slug, 'id' => $lesson->id]) }}"
                                                               class="lesson-link"
                                                            >
                                                                <p
                                                                    class="text-text14 lesson-item-title"
                                                                >
                                                                    {{ ucfirst($lesson->title) }}
                                                                </p>
                                                                <div class="d-flex align-items-center">
                                                                    @if($lesson->paid_lesson)
                                                                        <span class="badge premium-trial-badge">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/crown-icon.svg')}}"
                                                                            width="12.941px"
                                                                            height="12.941px"
                                                                            alt="Coin"
                                                                        />
                                                                        <span>PRO</span>
                                                                    </span>
                                                                    @elseif($lesson->trial_lesson)
                                                                        <span class="badge pro-trial-badge">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/crown-icon.svg')}}"
                                                                            width="12.941px"
                                                                            height="12.941px"
                                                                            alt="Coin"
                                                                        />
                                                                        <span>{{get_phrase('PRO TRIAL')}}</span>
                                                                    </span>
                                                                    @else
                                                                        <span class="badge free-badge">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/coin.svg')}}"
                                                                            width="11"
                                                                            height="11"
                                                                            alt="Coin"
                                                                        />
                                                                        <span>{{get_phrase('FREE')}}</span>
                                                                    </span>
                                                                    @endif
                                                                    @if($lesson->is_important)
                                                                        <span class="badge important-badge">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/important.svg')}}"
                                                                            width="11"
                                                                            height="11"
                                                                            alt="IMPORTANT"
                                                                        />
                                                                        <span>{{get_phrase('IMPORTANT')}}</span>
                                                                    </span>
                                                                    @endif
                                                                    @if($duration != '00:00:00')
                                                                        <span class="duration-small">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/clock.svg')}}"
                                                                            alt="Clock"
                                                                        />
                                                                        Thời lượng: <strong>{{ $duration }}</strong>
                                                                    </span>
                                                                    @endif
                                                                </div>
                                                            </a>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach

                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <img src="{{ asset('assets/img/bottom-banner.png') }}" alt="" class="bottom-banner" style="width: 100%;">
    </div>

@endsection

@push('js')
    <script>
        // Define ROUTES object for checkout
        const ROUTES = {
            checkout_course: "{{ route('checkout.course') }}",
            check_payment_success_order: "{{ route('check.payment.success.order') }}"
        };

        // Show message function
        function showMessage(message, isError = true) {
            // Kiểm tra xem toastr có được định nghĩa hay không
            if (typeof toastr !== 'undefined') {
                if (isError) {
                    toastr.error(message);
                } else {
                    toastr.success(message);
                }
            } else {
                // Fallback sử dụng alert nếu không có toastr
                alert(message);
            }
        }

        // Format currency function
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
                minimumFractionDigits: 0
            }).format(amount);
        }

        // Show QR code function
        function showQr(total_amount = null, transaction_content = null) {
            // Get bank info from backend
            const bankCode = "{{$bank_code ?? ""}}";
            const accountNumber = "{{$account_number ?? ""}}";
            const accountName = "{{$account_name ?? ""}}";
            // Set default values if not provided
            const amount = total_amount ? total_amount.toString() : "0";
            const addInfo = transaction_content || "";

            // Create the VietQR image URL
            const qrImageUrl = `https://api.vietqr.io/image/${bankCode}-${accountNumber}-w8onyVi.jpg?accountName=${encodeURIComponent(accountName)}&amount=${amount}&addInfo=${encodeURIComponent(addInfo)}`;

            // Get the container
            const container = document.getElementById("qrcode");
            if (container) {
                // Remove any existing img elements but keep the scanner div
                const existingImages = container.querySelectorAll("img");
                existingImages.forEach(img => img.remove());

                // Create and insert the image element
                const qrImageElement = document.createElement("img");
                qrImageElement.src = qrImageUrl;
                qrImageElement.style.display = "block";
                qrImageElement.className = "img-fluid";

                // Append the new QR image to the container
                container.appendChild(qrImageElement);
            }
        }

        // Check payment status function
        function checkPaymentStatus(orderId, courseId) {
            return $.post(ROUTES.check_payment_success_order, {
                _token: '{{ csrf_token() }}',
                order_id: orderId,
                course_id: courseId
            });
        }

        // Handle payment check status function
        function handlePaymentCheckStatus(order_id, courseId) {
            const checkInterval = 5000; // 5 giây

            // Kiểm tra trạng thái thanh toán mỗi 5 giây
            const paymentChecker = setInterval(function () {
                checkPaymentStatus(order_id, courseId)
                    .done(function (response) {
                        if (response && !response.error) {
                            clearInterval(paymentChecker);

                            // Hiển thị thông báo thành công
                            showMessage('Thanh toán thành công! Đang chuyển hướng...', false);

                            // Chuyển hướng đến trang khóa học
                            setTimeout(function() {
                                if (response.data && response.data.url_play_course) {
                                    window.location.href = response.data.url_play_course;
                                } else {
                                    window.location.reload();
                                }
                            }, 2000);
                        }
                    })
                    .fail(function () {
                        // Tiếp tục kiểm tra nếu có lỗi
                    });
            }, checkInterval);

            // Dừng kiểm tra sau 15 phút
            setTimeout(function() {
                clearInterval(paymentChecker);
            }, 15 * 60 * 1000);
        }
        function switchToPaymentTab(paymentData) {
            // Replace the entire user-info-container with payment interface
            const paymentHtml = `
                <div class="payment-methods-container" id="qrbank">
                    <h6 class="fw-bold mb-3">Thanh toán đơn hàng</h6>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="method-option h-100">
                                <div class="method-title text-center mb-3">
                                    <strong>Cách 1:</strong><br>
                                    Mở app ngân hàng/Ví và <strong>Quét mã QR</strong>
                                </div>
                                <div class="qr-container text-center">
                                    <img
                                        src="{{ asset('assets/frontend/course-shopee/assets/images/border-bank.png') }}"
                                        alt="Border Bank" class="border-bank img-fluid mb-2"/>

                                    <div id="qrcode" class="img-fluid mb-2 img-bank mx-auto">
                                        <div class="scanner"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="method-option h-100">
                                <div class="method-title text-center mb-3">
                                    <strong>Cách 2:</strong><br>
                                    Chuyển khoản <strong>thủ công</strong> theo thông tin
                                </div>
                                <div class="body-method-option">
                                    <div class="bank-info">
                                        <div class="bank-info-row">
                                            <span class="bank-info-label">Ngân hàng:</span>
                                            <span class="bank-info-value bank-name">{!! getBankData($bank_code ?? "") !!}</span>
                                        </div>
                                        <div class="bank-info-row">
                                            <span class="bank-info-label">Thụ hưởng:</span>
                                            <span class="bank-info-value account-name">{{$account_name ?? '' }}</span>
                                        </div>
                                        <div class="bank-info-row">
                                            <span class="bank-info-label">Số tài khoản:</span>
                                            <span class="bank-info-value account-number">
                                                <span class="account-number-text">{{ $account_number ?? '' }}</span>
                                                <i class="bi bi-copy copy-value text-black" data-value="{{ $account_number?? '' }}"></i>
                                            </span>
                                        </div>
                                        <div class="bank-info-row">
                                            <span class="bank-info-label">Số tiền:</span>
                                            <span class="bank-info-value bank_total_amount">${formatCurrency(paymentData.total_amount)}</span>
                                        </div>
                                        <div class="bank-info-row">
                                            <span class="bank-info-label">Nội dung CK:</span>
                                            <div>
                                                <span class="bank-info-value bank_transaction_content">${paymentData.transaction_content}</span>
                                                <i class="bi bi-copy copy-value text-black" data-value="${paymentData.transaction_content}"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="payment-note mt-4">
                        <p><strong>Lưu ý:</strong></p>
                        <ul>
                            <li>Vui lòng chuyển khoản <strong>ĐÚNG SỐ TIỀN</strong> và <strong>ĐÚNG NỘI DUNG</strong> để hệ thống tự động xác nhận thanh toán.</li>
                            <li>Sau khi chuyển khoản thành công, hệ thống sẽ tự động kích hoạt khóa học trong vòng <strong>1-2 phút</strong>.</li>
                            <li>Nếu có vấn đề, vui lòng liên hệ hỗ trợ.</li>
                        </ul>
                    </div>
                </div>
            `;

            // Replace only the pricing summary section (from hot-deal to total-price)
            $('.hot-deal').parent().find('.hot-deal, .price-info, .total-price, #coupon_form').remove();
            $('.hot-deal').parent().prepend(paymentHtml);

            // Update URL with anchor
            if (history.pushState) {
                const newUrl = window.location.href.split('#')[0] + '#qrbank';
                history.pushState(null, null, newUrl);
            }

            // Show QR code after DOM is updated
            setTimeout(function() {
                showQr(paymentData.total_amount, paymentData.transaction_content);

                // Scroll to QR section smoothly
                document.getElementById('qrbank').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
        }

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showMessage('Đã sao chép vào clipboard!', false);
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showMessage('Đã sao chép vào clipboard!', false);
            });
        }

        // Remove invalid feedback function
        function removeInvalid() {
            $('.invalid-feedback').remove();
            $('.is-invalid').removeClass('is-invalid');
        }

        // Show loading state on button
        function showButtonLoading() {
            const $button = $('#btn-checkout-regis');
            $button.prop('disabled', true);
            $button.data('original-text', $button.text());
            $button.html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');
        }

        // Hide loading state on button
        function hideButtonLoading() {
            const $button = $('#btn-checkout-regis');
            $button.prop('disabled', false);
            $button.html($button.data('original-text') || 'Mua ngay');
        }

        // Handle checkout payment function
        function onHandlecheckoutPayment(token) {
            if (!token) {
                showMessage('Vui lòng xác thực captcha', true);
                return false;
            }

            // Show loading state
            showButtonLoading();

            // Remove previous errors
            removeInvalid();

            // Check if user is authenticated
            const isAuthenticated = {{ auth()->check() ? 'true' : 'false' }};

            let formData;
            if (isAuthenticated) {
                // For authenticated users, only send course and pricing info
                formData = $('#form-checkout-regis').find('input[name="course_id"], input[name="coupon_code"], input[name="pricing_plan_id"], input[name="_token"]').serialize();
            } else {
                // For guests, send all form data
                formData = $('#form-checkout-regis').serialize();
            }

            // Submit form
            $.post(ROUTES.checkout_course, formData, function (response) {
                console.log('Backend response:', response);

                // Hide loading state
                hideButtonLoading();

                if (!response.error) {
                    // Success - handle different response types
                    console.log('Response data:', response.data);
                    console.log('Total amount:', response.data ? response.data.total_amount : 'undefined');
                    console.log('Has url_play_course:', response.data ? !!response.data.url_play_course : false);

                    // Check if this is a free course or already enrolled
                    if (response.data && response.data.url_play_course &&
                        (response.data.total_amount === undefined || response.data.total_amount == 0)) {
                        // Free course or already enrolled - redirect to course immediately
                        console.log('Free course or already enrolled, redirecting...');
                        showMessage(response.message || 'Đăng ký thành công!', false);
                        setTimeout(function() {
                            window.location.href = response.data.url_play_course;
                        }, 1500);
                    } else if (response.data && response.data.total_amount && response.data.total_amount > 0) {
                        // Payment required - show payment interface
                        console.log('Payment required, showing payment interface...');
                        console.log('Order ID:', response.data.order_id);
                        console.log('Transaction content:', response.data.transaction_content);

                        showMessage('Đơn hàng đã được tạo. Vui lòng thanh toán để hoàn tất.', false);

                        // Switch to payment interface
                        switchToPaymentTab(response.data);

                        // Start payment status checking
                        handlePaymentCheckStatus(response.data.order_id, response.data.course_id);
                    } else {
                        console.log('Unknown response type:', response);
                        showMessage(response.message || 'Có lỗi xảy ra!', true);
                        hideButtonLoading();
                    }
                } else {
                    console.log('Response has error:', response.message);
                    showMessage(response.message || 'Có lỗi xảy ra, vui lòng thử lại!', true);
                    hideButtonLoading();
                }
            }).fail(function (xhr) {
                // Hide loading state
                hideButtonLoading();

                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response && response.error && response.message) {
                        // Check if it's a string message (for login errors)
                        if (typeof response.message === 'string') {
                            showMessage(response.message, true);
                            return;
                        }

                        // Display errors for each input field (for registration errors)
                        if (response.message.email) {
                            $('#payment_register_email').addClass('is-invalid');
                            $('#payment_register_email').after(`<div class="invalid-feedback">${response.message.email}</div>`);
                        }
                        if (response.message.password) {
                            $('#payment_register_password').addClass('is-invalid');
                            $('#payment_register_password').after(`<div class="invalid-feedback">${response.message.password}</div>`);
                        }
                        if (response.message.phone) {
                            $('#payment_register_phone').addClass('is-invalid');
                            $('#payment_register_phone').parents(".phone-group").addClass('is-invalid');
                            $('#payment_register_phone').parents(".phone-group").after(`<div class="invalid-feedback">${response.message.phone}</div>`);
                        }
                        if (response.message.captcha) {
                            $('.g-recaptcha-checkout').addClass('is-invalid');
                            $('.g-recaptcha-checkout').after(`<div class="invalid-feedback">${response.message.captcha}</div>`);
                        }
                    }
                } catch (e) {
                    // Handle CSRF token expired error
                    if (xhr.status === 419) {
                        showMessage('Phiên làm việc đã hết hạn. Vui lòng tải lại trang.', true);
                        setTimeout(function() {
                            location.reload();
                        }, 3000);
                    } else if (xhr.status === 500) {
                        showMessage('Lỗi hệ thống, vui lòng thử lại sau!', true);
                    } else if (xhr.status === 404) {
                        showMessage('Không tìm thấy khóa học!', true);
                    } else if (xhr.status === 403) {
                        showMessage('Bạn không có quyền thực hiện hành động này!', true);
                    } else {
                        showMessage('Có lỗi xảy ra, vui lòng kiểm tra kết nối mạng và thử lại!', true);
                    }
                }
            });
        }

        // Check if reCAPTCHA is loaded
        function checkRecaptcha() {
            return typeof grecaptcha !== 'undefined';
        }

        $(document).ready(function () {
            // Check if user is authenticated
            const isAuthenticated = {{ auth()->check() ? 'true' : 'false' }};

            // Only setup login/register toggle for guest users
            if (!isAuthenticated) {
                // Function to handle login link click
                function handleLoginClick(e) {
                    e.preventDefault();

                    removeInvalid();
                    $(".is-show").hide();
                    $(".change-login-register").html(`Bạn chưa có tài khoản? <a class="register-link-modal" style="cursor: pointer; color: #007bff; text-decoration: underline;">Ấn vào đây để đăng ký</a><input type="hidden" value="1" name="is_login">`);
                    $("#btn-checkout-regis").text("Đăng nhập");
                }

                // Function to handle register link click
                function handleRegisterClick(e) {
                    e.preventDefault();

                    removeInvalid();
                    $(".is-show").show();
                    $(".change-login-register").html(`Bạn đã có tài khoản? <a class="login-link" style="cursor: pointer; color: #007bff; text-decoration: underline;">Ấn vào đây để đăng nhập</a>`);
                    $("#btn-checkout-regis").text("Mua ngay");
                }

                // Event delegation for dynamically created elements
                $("body").on('click', '.login-link', handleLoginClick);
                $("body").on('click', '.register-link-modal', handleRegisterClick);

                // Direct binding for initial elements
                $('.login-link').on('click', handleLoginClick);
                $('.register-link-modal').on('click', handleRegisterClick);
            }

            // Add fallback click handler if reCAPTCHA fails
            $('#btn-checkout-regis').on('click', function(e) {
                // Check if reCAPTCHA is enabled and working
                if ($(this).hasClass('g-recaptcha') && checkRecaptcha()) {
                    // Let reCAPTCHA handle it - don't prevent default
                } else {
                    e.preventDefault();
                    // Call function directly with a dummy token for testing
                    onHandlecheckoutPayment('direct-call-token');
                }
            });

            // Copy button event handlers
            $(document).on('click', '.copy-value', function() {
                const value = $(this).attr('data-value');
                if (value) {
                    copyToClipboard(value);
                }
            });



            // Password toggle functionality
            const toggleButtons = document.querySelectorAll(".password-toggle");

            toggleButtons.forEach((button) => {
                button.addEventListener("click", function () {
                    const input = this.previousElementSibling;
                    const icon = this.querySelector("i");

                    if (input.type === "password") {
                        input.type = "text";
                        icon.classList.replace("bi-eye-slash", "bi-eye");
                    } else {
                        input.type = "password";
                        icon.classList.replace("bi-eye", "bi-eye-slash");
                    }
                });
            });
        });

    </script>

    <script>
        function playVideo() {
            // If there's a preview video, open it in a modal or redirect to video player
            var videoUrl = "https://www.w3schools.com/html/mov_bbb.mp4";

            // Create a modal to show the video
            var modal = document.createElement('div');
            modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

            var videoContainer = document.createElement('div');
            videoContainer.style.cssText = `
            position: relative;
            width: 90%;
            max-width: 800px;
            aspect-ratio: 16/9;
        `;

            var closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
            position: absolute;
            top: -40px;
            right: 0;
            background: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            z-index: 10000;
        `;
            closeBtn.onclick = function () {
                document.body.removeChild(modal);
            };

            var video = document.createElement('video');
            video.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: cover;
        `;
            video.controls = true;
            video.autoplay = true;
            video.src = videoUrl;

            videoContainer.appendChild(closeBtn);
            videoContainer.appendChild(video);
            modal.appendChild(videoContainer);
            document.body.appendChild(modal);

            // Close modal when clicking outside
            modal.onclick = function (e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            };
        }

        // Initialize AOS animations
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Check if URL has #qrbank anchor and scroll to it on page load
        $(document).ready(function() {
            if (window.location.hash === '#qrbank') {
                setTimeout(function() {
                    const qrElement = document.getElementById('qrbank');
                    if (qrElement) {
                        qrElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }, 500);
            }
        });
    </script>
@endpush
