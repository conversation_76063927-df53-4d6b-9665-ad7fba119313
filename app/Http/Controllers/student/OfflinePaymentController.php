<?php

namespace App\Http\Controllers\student;

use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\CartItem;
use App\Models\Coupon;
use App\Models\Enrollment;
use App\Models\Enrollments;
use App\Models\FileUploader;
use App\Models\OfflinePayment;
use App\Models\User;
use App\Models\Course;
use Carbon\Carbon;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;

class OfflinePaymentController extends Controller
{
    public function store(Request $request)
    {
        // check amount
        $payment_details = Session::get('payment_details');
        $item_id_arr = [];
        foreach ($payment_details['items'] as $item) {
            $item_id_arr[] = $item['id'];
        }

        $rules = [
            'doc' => 'required|mimes:jpeg,jpg,pdf,txt,png,docx,doc|max:3072',
        ];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }


        $file = $request->doc;
        $file_name = Str::random(20) . '.' . $file->extension();
        $path = 'uploads/offline_payment/' . slugify(auth()->user()->name) . '/' . $file_name;
        FileUploader::upload($file, $path, null, null, 300);

        $offline_payment['user_id'] = auth()->user()->id;
        $offline_payment['item_type'] = $request->item_type;
        $offline_payment['items'] = json_encode($item_id_arr);
        $offline_payment['tax'] = $payment_details['tax'];
        $offline_payment['total_amount'] = $payment_details['payable_amount'];
        $offline_payment['doc'] = $path;
        $offline_payment['coupon'] = $payment_details['coupon'];

        // insert offline payment history
        OfflinePayment::insert($offline_payment);

        // remove from cart
        if ($request->item_type == 'course') {
            $url = 'purchase.history';
            CartItem::whereIn('course_id', $item_id_arr)->where('user_id', auth()->user()->id)->delete();
        } elseif ($request->item_type == 'bootcamp') {
            $url = 'bootcamps';
        } elseif ($request->item_type == 'package') {
            $url = 'team.packages';
        }

        // return to courses
        Session::flash('success', 'Thanh toán sẽ được hoàn tất sau khi quản trị viên xem xét và phê duyệt.');
        return redirect()->route($url);
    }


    public function checkoutCourse(Request $request)
    {
        $input = $request->all();
        if (auth()->check()) {
            $user = auth()->user();
        } else {
            if (get_settings('recaptcha_status') == true && check_recaptcha($input['g-recaptcha-response']) == false) {

                return response()->json(['error' => true, 'message' => ["captcha" => 'Xác thực Recaptcha thất bại']], 422);

            }
            if ($request->input('is_login') == 1) {

                $validator = Validator::make($request->only(['email', 'password']), [
                    'email' => ['required', 'string', 'email'],
                    'password' => ['required', "string"],
                ], [
                    'email.required' => 'Vui lòng nhập email',
                    'email.email' => 'Email không đúng định dạng',
                    'password.required' => 'Vui lòng nhập mật khẩu',
                ]);

                if ($validator->fails()) {
                    return response()->json(['error' => true, 'message' => $validator->errors()], 422);
                }
                $user = User::where('email', $request->email)->first();
                if (!$user) {
                    return response()->json(['error' => true, 'message' => 'Không tìm thấy email'], 422);
                }
                if (!Hash::check($request->password, $user->password)) {
                    return response()->json(['error' => true, 'message' => 'Mật khẩu không chính xác'], 422);
                }
                Auth::login($user);
            } else {
                $validator = Validator::make($request->all(), [
                    'email' => ['required', 'string', 'email', 'unique:users,email'],
                    'password' => ['required', Rules\Password::defaults()],
                    'phone' => ['required', 'string', 'max:15']
                ], [
                    'email.required' => 'Vui lòng nhập email',
                    'email.email' => 'Email không đúng định dạng',
                    'email.unique' => 'Email này đã được sử dụng',
                    'password.required' => 'Vui lòng nhập mật khẩu',
                    'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự',
                    'phone.required' => 'Vui lòng nhập số điện thoại',
                    'phone.max' => 'Số điện thoại không được quá 15 ký tự',
                ]);

                if ($validator->fails()) {
                    return response()->json(['error' => true, 'message' => $validator->errors()], 422);
                    // return redirect()->back()
                    //     ->withErrors($validator)
                    //     ->withInput()
                    //     ->with('show_payment_modal', true)
                    //     ->with('active_tab', 'tab_1');
                }
                if (empty($request->name)) {
                    $emailParts = explode('@', $request->email);
                    $username = $emailParts[0];
                    $request->merge(['name' => $username]);
                }
                $user_data = [
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'role' => 'student',
                    'status' => 1,
                    'password' => Hash::make($request->password),
                ];

                if (get_settings('student_email_verification') != 1) {
                    $user_data['email_verified_at'] = Carbon::now();
                }

                $user = User::create($user_data);

                try {
                    event(new Registered($user));
                } catch (\Exception $e) {

                }
                Auth::login($user);
            }


        }
        // Kiểm tra xem khóa học có tồn tại không
        $course = Course::find($request->course_id);
        if (!$course) {
            return response()->json(['error' => true, 'message' => 'Không tìm thấy khóa học']);
        }
        $enrollment_status = enroll_status(isset($course) && is_object($course) ? $course->id : 0, $user->id);
        if ($enrollment_status == 'valid') {

            return response()->json([
                'error' => false,
                "data" => [
                    "url_play_course" => route('course.player', ['slug' => $course->slug]),
                    "html_profile" => view('ajax.html-menu-user-profile', ["user" => $user, "enrollment_status" => $enrollment_status])->render(),
                ],
                'message' => "Bạn đã đăng ký khóa học này"
            ]);
        }

        $coupon = Coupon::where('code', $request->input("coupon_code"))
            ->whereIn("status", [1, 2])
            ->where(function ($query) {
                $query->where("quantity", ">", 0)
                    ->orWhereNull("quantity");
            })
            ->where("expiry", ">=", time())
            ->where(function ($query) use ($course) {
                $query->where("course_id", $course->id)
                    ->orWhere("course_id", null);
            })
            ->first();
        // Kiểm tra xem đơn hàng đã tồn tại và đã thanh toán chưa
        $order = OfflinePayment::where('course_id', $course->id)
            ->where("user_id", $user->id)
            ->where("item_type", "course")
            ->orderBy("id", "desc")
            ->first();
        if ($order) {
            if ($order->status == 1) {
                // Kiểm tra nếu khóa học hết hạn thì cho phép mua lại
                if ($enrollment_status == 'expired') {
                    // Khóa học hết hạn, cho phép tạo đơn mới để gia hạn
                    // Bỏ qua đơn hàng cũ, tạo đơn mới
                } else {
                    return response()->json(['error' => true, 'message' => 'Bạn đã thanh toán cho khóa học này']);
                }
            }
            // Nếu đơn hàng đã tồn tại và chưa thanh toán thì cập nhật lại tổng tiền
            if ($order->status == 0 && $enrollment_status != 'expired') {
                $pricingPlanId = $request->input('pricing_plan_id');
                $total_amount = $this->caculateTotalAmount($course, $coupon, $pricingPlanId);
                if ($total_amount != $order->total_amount) {
                    $dataAffiliate = $this->getDataAffiliate($total_amount);
                    $order->update(array_merge($dataAffiliate, [
                        'total_amount' => $total_amount,
                        'pricing_plan_id' => $pricingPlanId, // Cập nhật pricing plan ID
                    ]));
                }
                if ($total_amount == 0) {
                    return response()->json([
                        'error' => false,
                        "data" => [
                            "url_play_course" => route('course.player', ['slug' => $course->slug]),
                            "html_profile" => view('ajax.html-menu-user-profile', ["user" => $user, "enrollment_status" => $enrollment_status])->render(),
                            "total_amount" => $total_amount,
                            "transaction_content" => $order->transaction_content,
                            "order_id" => $order->id,
                            "course_id" => $course->id,
                            // Tình số giây còn lại từ lúc tạo đơn đến giờ hiện tại
                            // Không tính mili second

                            "time" => ceil(15 * 60 - $order->created_at->diffInSeconds(Carbon::now())),
                        ],
                        'message' => 'Thanh toán thành công'
                    ]);
                }

                return response()->json([
                    'error' => false,
                    "data" => [
                        "html_profile" => view('ajax.html-menu-user-profile', ["user" => $user, "enrollment_status" => $enrollment_status])->render(),
                        "total_amount" => $total_amount,
                        "transaction_content" => $order->transaction_content,
                        "order_id" => $order->id,
                        "course_id" => $course->id,
                        // Tình số giây còn lại từ lúc tạo đơn đến giờ hiện tại
                        // Không tính mili second

                        "time" => ceil(15 * 60 - $order->created_at->diffInSeconds(Carbon::now())),
                    ],
                    'message' => 'Thanh toán thành công'
                ]);
            }

        }
        // Nếu đơn hàng chưa tồn tại hoặc đã bị hủy hoặc khóa học hết hạn thì tạo mới
        $pricingPlanId = $request->input('pricing_plan_id');
        $offlinePayment = $this->createOfflinePayment($course, $user, $coupon, $pricingPlanId);

        if ($coupon && $coupon->quantity > 0) {
            $coupon->decrement('quantity');
        }
        $enrollment_status = enroll_status(isset($course) && is_object($course) ? $course->id : 0, $user->id);
        if ($offlinePayment->total_amount == 0) {
            return response()->json([
                'error' => false,
                "data" => [
                "url_play_course" => route('course.player', ['slug' => $course->slug]),
                    "html_profile" => view('ajax.html-menu-user-profile', ["user" => $user, "enrollment_status" => $enrollment_status])->render(),
                    "total_amount" => $offlinePayment->total_amount,
                    "transaction_content" => $offlinePayment->transaction_content,
                    "order_id" => $offlinePayment->id,
                    "course_id" => $course->id,
                    "time" => ceil(15 * 60 - $offlinePayment->created_at->diffInSeconds(Carbon::now())),
                ],
                'message' => 'Thanh toán thành công'
            ]);
        }
        return response()->json([
            'error' => false,
            "data" => [
                "html_profile" => view('ajax.html-menu-user-profile', ["user" => $user, "enrollment_status" => $enrollment_status])->render(),
                "total_amount" => $offlinePayment->total_amount,
                "transaction_content" => $offlinePayment->transaction_content,
                "order_id" => $offlinePayment->id,
                "course_id" => $course->id,
                "time" => ceil(15 * 60 - $offlinePayment->created_at->diffInSeconds(Carbon::now())),
            ],
            'message' => 'Đơn hàng đã được tạo. Vui lòng thanh toán để hoàn tất đăng ký.'
        ]);

    }

    private function createOfflinePayment($course, $user, $coupon, $pricingPlanId = null)
    {
        //Tổng tiền khóa học
        $total_amount = $this->caculateTotalAmount($course, $coupon, $pricingPlanId);

        $dataAffiliate = $this->getDataAffiliate($total_amount);

        // Kiểm tra xem có phải là gia hạn không
        $existingEnrollment = Enrollment::where('course_id', $course->id)
            ->where('user_id', $user->id)
            ->first();
        $isRenewal = $existingEnrollment && enroll_status($course->id, $user->id) == 'expired';

        // T/H không có đơn hàng
        $offline_payment['user_id'] = $user->id;
        $offline_payment['item_type'] = "course";
        $offline_payment['items'] = json_encode([$course->id]);
        $offline_payment['tax'] = 0;
        $offline_payment['total_amount'] = $total_amount;
        $offline_payment['doc'] = null;
        $offline_payment['course_id'] = $course->id;
        $offline_payment['coupon'] = is_object($coupon) ? $coupon->code : null;
        $offline_payment['transaction_content'] = $total_amount > 0 ? strtoupper(Str::random(12)) : ""; // Nội dung giao dịch
        $offline_payment['status'] = $total_amount > 0 ? 0 : 1;
        $offline_payment['payment_type'] = 'sepay';
        $offline_payment['pricing_plan_id'] = $pricingPlanId; // Lưu pricing plan ID
        $offline_payment = array_merge($offline_payment, $dataAffiliate);

        // insert offline payment history
        $payment = OfflinePayment::create($offline_payment);

        // Chỉ tạo enrollment khi khóa học miễn phí (total_amount = 0)
        // Khi có phí thì chờ webhook từ ngân hàng xác nhận thanh toán
        if ($total_amount == 0) {
            // Khóa học miễn phí - tạo enrollment ngay lập tức
            if ($existingEnrollment) {
            // Cập nhật enrollment hiện có (trường hợp gia hạn hoặc thanh toán)
            $updateData = [
                'enrollment_type' => "paid",
                'entry_date' => time(),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Thêm pricing_plan_id nếu có
            if ($pricingPlanId && $course->hasMultiplePricingPlans()) {
                $updateData['pricing_plan_id'] = $pricingPlanId;
                $updateData['paid_amount'] = $total_amount;

                // Tính ngày hết hạn
                $plan = $course->getPricingPlanById($pricingPlanId);
                if ($plan) {
                    if ($plan['duration_type'] === 'lifetime') {
                        $updateData['expiry_date'] = null; // Trọn đời
                    } elseif ($plan['duration_type'] === 'months' && isset($plan['duration_value'])) {
                        // Luôn tính từ ngày hiện tại cho mua mới hoặc gia hạn với số tháng chính xác
                        $months = $plan['duration_value'];
                        $updateData['expiry_date'] = strtotime("+" . $months . " months");
                    }
                }
            } else {
                // Logic cũ cho trường hợp không có pricing plan
                if ($course->expiry_period > 0) {
                    $months = $course->expiry_period;
                    $updateData['expiry_date'] = strtotime("+" . $months . " months");
                } else {
                    $updateData['expiry_date'] = null;
                }
            }

            $existingEnrollment->update($updateData);
        } else {
            // Xóa tất cả enrollment cũ trước khi tạo mới (để tránh duplicate)
            Enrollment::where('course_id', $course->id)
                ->where('user_id', $user->id)
                ->delete();

            // Tạo enrollment mới
            $enroll['course_id'] = $course->id;
            $enroll['user_id'] = $user->id;
            $enroll['enrollment_type'] = "paid";
            $enroll['entry_date'] = time();
            $enroll['created_at'] = date('Y-m-d H:i:s');
            $enroll['updated_at'] = date('Y-m-d H:i:s');

            // Thêm pricing_plan_id nếu có
            if ($pricingPlanId && $course->hasMultiplePricingPlans()) {
                $enroll['pricing_plan_id'] = $pricingPlanId;
                $enroll['paid_amount'] = $total_amount;

                // Tính ngày hết hạn theo plan
                $plan = $course->getPricingPlanById($pricingPlanId);
                if ($plan) {
                    if ($plan['duration_type'] === 'lifetime') {
                        $enroll['expiry_date'] = null; // Trọn đời
                    } elseif ($plan['duration_type'] === 'months' && isset($plan['duration_value'])) {
                        // Tính từ ngày hiện tại với số tháng chính xác
                        $months = $plan['duration_value'];
                        $enroll['expiry_date'] = strtotime("+" . $months . " months");
                    }
                }
            } else {
                // Logic cũ cho trường hợp không có pricing plan
                if ($course->expiry_period > 0) {
                    $months = $course->expiry_period;
                    $enroll['expiry_date'] = strtotime("+" . $months . " months");
                } else {
                    $enroll['expiry_date'] = null;
                }
            }

            // insert a new enrollment
            Enrollment::insert($enroll);
            }
        }
        // Nếu có phí (total_amount > 0), chỉ tạo bill và chờ webhook xác nhận thanh toán

        return $payment;
    }

    private function caculateTotalAmount($course, $coupon, $pricingPlanId = null)
    {
        // Nếu có pricing plan, tính giá theo plan
        if ($pricingPlanId && $course->hasMultiplePricingPlans()) {
            $price = $course->getPlanCurrentPrice($pricingPlanId);
        } else {
            // Logic cũ cho single pricing
            if ($course->discounted_price < $course->price) {
                $price = $course->discounted_price;
            } elseif ($course->is_paid == 0) {
                $price = 0;
            } elseif ($course->discount_flag == 1) {
                $price = $course->discounted_price;
            } else {
                $price = $course->price;
            }
        }

        if ($price == 0) {
            return 0;
        }

        $total_amount = $price;

        // Kiểm tra nếu quantity là null hoặc > 0
        if ($coupon && ($coupon->quantity === null || $coupon->quantity > 0)) {
            // Số tiền giảm giá coupon
            $coupon_amount = $total_amount * $coupon->discount / 100;
            // Tổng tiền cần thanh toán sau khi giảm giá
            $total_amount -= $coupon_amount;
        }

        return $total_amount;
    }

    public function checkPaymentSuccessOrder(Request $request)
    {
        $order = OfflinePayment::where("user_id", auth()->user()->id)
            ->where("item_type", "course")
            ->where("course_id", $request->input("course_id"))
            // ->where("status", 0)
            ->where("payment_type", "sepay")
            ->find($request->input("order_id"));
        if (!$order) {
            return response()->json(['error' => true, 'is_cancel' => true, 'message' => 'Không tìm thấy đơn hàng!']);
        }
        if ($order->status == 1) {
            return response()->json(['error' => false, 'is_cancel' => false, 'message' => 'Đơn hàng đã được thanh toán']);
        }
        if ($order->status == 2 || $order->created_at->addMinutes(15) < Carbon::now()) {
            if ($order->status == 0) {
                $order->update(["status" => 2]);
                $coupon = Coupon::select('id', 'quantity')
                    ->where("code", $order->coupon)->first();
                if ($coupon && $coupon->quantity !== null) {
                    $coupon->increment('quantity');
                }
            }
            return response()->json(['error' => true, 'is_cancel' => true, 'message' => 'Đã hết hạn thanh toán! Vui lòng tải lại trang để thực hiện giao dịch mới!']);
        }

        return response()->json(['error' => true, 'is_cancel' => false, 'message' => 'Đơn hàng chưa được thanh toán!']);
    }

    private function getDataAffiliate($total_amount): array
    {

        $data['is_approve_affiliate'] = true;
        if (Cookie::has('affiliate_ref')) {
            //Người được hưởng tiền affiliate sẽ được lấy từ cookie với key là ref=user_id.
            $ref = Cookie::get('affiliate_ref');
            $ref = hex2bin($ref);
            if (str_starts_with($ref, 'KH-')) {
                if ($this->checkEnableAffiliate()) {
                    $affiliate_amount = $this->getAffiliateAmount($total_amount);
                    if ($affiliate_amount > 0) {
                        $affiliate_id = str_replace('KH-', '', $ref);
                        $userAffiliate = User::find($affiliate_id);
                        if (is_object($userAffiliate)) {
                            $data['affiliate_amount'] = $affiliate_amount; //Số tiền nhận được từ affiliate
                            $data['is_approve_affiliate'] = false; //Trạng thái xác nhận đã duyệt chi tiền affiliate
                            $data['affiliate_id'] = $userAffiliate->id;//ID người được hưởng tiền affiliate
                            $data['is_add_earning_affiliate'] = 0;
                        }
                    }
                }
            }
        }
        return $data;
    }

    private function checkEnableAffiliate()
    {
        return get_settings('allow_affiliate') == 1;
    }

    private function getAffiliateAmount($total_amount)
    {
        return $total_amount * get_settings('affiliate_revenue') / 100;
    }
}
